package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.EventObject

class EventObjectAdapter(private var eventList: List<EventObject>) : RecyclerView.Adapter<EventObjectAdapter.EventObjectViewHolder>() {

    class EventObjectViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EventObjectViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_event_object, parent, false)
        return EventObjectViewHolder(view)
    }

    override fun onBindViewHolder(holder: EventObjectViewHolder, position: Int) {
        val event = eventList[position]
        
        holder.textShortUuid.text = event.shortUuid
        holder.textTitle.text = event.title
        holder.textCreated.text = event.created
    }

    override fun getItemCount(): Int = eventList.size

    fun updateData(newEventList: List<EventObject>) {
        eventList = newEventList
        notifyDataSetChanged()
    }
}
