package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.ExpenditureObject
import ir.rahavardit.ariel.data.model.IncomeObject
import ir.rahavardit.ariel.data.model.EventObject

class HomePagerAdapter : RecyclerView.Adapter<HomePagerAdapter.TabViewHolder>() {

    private var incomeList: List<IncomeObject> = emptyList()
    private var expenditureList: List<ExpenditureObject> = emptyList()
    private var eventList: List<EventObject> = emptyList()

    sealed class TabViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        class IncomeViewHolder(itemView: View) : TabViewHolder(itemView) {
            val recyclerView: RecyclerView = itemView.findViewById(R.id.recycler_income)
        }
        
        class ExpenditureViewHolder(itemView: View) : TabViewHolder(itemView) {
            val recyclerView: RecyclerView = itemView.findViewById(R.id.recycler_expenditure)
        }
        
        class EventViewHolder(itemView: View) : TabViewHolder(itemView) {
            val recyclerView: RecyclerView = itemView.findViewById(R.id.recycler_events)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return position // 0 = Income, 1 = Expenditure, 2 = Events
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        return when (viewType) {
            0 -> {
                val view = LayoutInflater.from(parent.context).inflate(R.layout.tab_income, parent, false)
                TabViewHolder.IncomeViewHolder(view)
            }
            1 -> {
                val view = LayoutInflater.from(parent.context).inflate(R.layout.tab_expenditure, parent, false)
                TabViewHolder.ExpenditureViewHolder(view)
            }
            2 -> {
                val view = LayoutInflater.from(parent.context).inflate(R.layout.tab_events, parent, false)
                TabViewHolder.EventViewHolder(view)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
        when (holder) {
            is TabViewHolder.IncomeViewHolder -> {
                holder.recyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
                holder.recyclerView.adapter = IncomeAdapter(incomeList)
            }
            is TabViewHolder.ExpenditureViewHolder -> {
                holder.recyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
                holder.recyclerView.adapter = ExpenditureAdapter(expenditureList)
            }
            is TabViewHolder.EventViewHolder -> {
                holder.recyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
                holder.recyclerView.adapter = EventObjectAdapter(eventList)
            }
        }
    }

    override fun getItemCount(): Int = 3 // Income, Expenditure, Events

    fun updateData(
        newIncomeList: List<IncomeObject>,
        newExpenditureList: List<ExpenditureObject>,
        newEventList: List<EventObject>
    ) {
        incomeList = newIncomeList
        expenditureList = newExpenditureList
        eventList = newEventList
        notifyDataSetChanged()
    }
}
