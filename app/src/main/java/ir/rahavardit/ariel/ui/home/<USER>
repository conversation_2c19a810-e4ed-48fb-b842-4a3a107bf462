package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.IncomeObject
import java.text.NumberFormat
import java.util.Locale

class IncomeAdapter(private var incomeList: List<IncomeObject>) : RecyclerView.Adapter<IncomeAdapter.IncomeViewHolder>() {

    class IncomeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAmount: TextView = itemView.findViewById(R.id.text_amount)
        val textBank: TextView = itemView.findViewById(R.id.text_bank)
        val textCategory: TextView = itemView.findViewById(R.id.text_category)
        val textTags: TextView = itemView.findViewById(R.id.text_tags)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): IncomeViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_income, parent, false)
        return IncomeViewHolder(view)
    }

    override fun onBindViewHolder(holder: IncomeViewHolder, position: Int) {
        val income = incomeList[position]
        
        holder.textShortUuid.text = income.shortUuid
        holder.textTitle.text = income.title
        
        // Format amount with Persian number formatting
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
        holder.textAmount.text = "${numberFormat.format(income.amount)} ریال"
        
        holder.textBank.text = "بانک: ${income.bank.title}"
        holder.textCategory.text = "دسته: ${income.category.persianName}"
        holder.textTags.text = if (income.tagsNames.isNotEmpty()) {
            "برچسب‌ها: ${income.tagsNames.joinToString(", ")}"
        } else {
            "بدون برچسب"
        }
        holder.textCreated.text = income.created
    }

    override fun getItemCount(): Int = incomeList.size

    fun updateData(newIncomeList: List<IncomeObject>) {
        incomeList = newIncomeList
        notifyDataSetChanged()
    }
}
