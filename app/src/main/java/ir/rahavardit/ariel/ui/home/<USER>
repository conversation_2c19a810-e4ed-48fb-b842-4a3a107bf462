package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.ExpenditureObject
import java.text.NumberFormat
import java.util.Locale

class ExpenditureAdapter(private var expenditureList: List<ExpenditureObject>) : RecyclerView.Adapter<ExpenditureAdapter.ExpenditureViewHolder>() {

    class ExpenditureViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAmount: TextView = itemView.findViewById(R.id.text_amount)
        val textBank: TextView = itemView.findViewById(R.id.text_bank)
        val textCategory: TextView = itemView.findViewById(R.id.text_category)
        val textTags: TextView = itemView.findViewById(R.id.text_tags)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ExpenditureViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_expenditure, parent, false)
        return ExpenditureViewHolder(view)
    }

    override fun onBindViewHolder(holder: ExpenditureViewHolder, position: Int) {
        val expenditure = expenditureList[position]
        
        holder.textShortUuid.text = expenditure.shortUuid
        holder.textTitle.text = expenditure.title
        
        // Format amount with Persian number formatting
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
        holder.textAmount.text = "${numberFormat.format(expenditure.amount)} ریال"
        
        holder.textBank.text = "بانک: ${expenditure.bank.title}"
        holder.textCategory.text = "دسته: ${expenditure.category.title}"
        holder.textTags.text = if (expenditure.tagsNames.isNotEmpty()) {
            "برچسب‌ها: ${expenditure.tagsNames.joinToString(", ")}"
        } else {
            "بدون برچسب"
        }
        holder.textCreated.text = expenditure.created
    }

    override fun getItemCount(): Int = expenditureList.size

    fun updateData(newExpenditureList: List<ExpenditureObject>) {
        expenditureList = newExpenditureList
        notifyDataSetChanged()
    }
}
