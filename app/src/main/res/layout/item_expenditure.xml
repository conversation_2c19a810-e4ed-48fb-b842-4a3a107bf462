<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/text_short_uuid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="UUID"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/text_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="عنوان"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary" />

            <TextView
                android:id="@+id/text_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="مبلغ"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@android:color/holo_red_dark" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <TextView
                android:id="@+id/text_bank"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="بانک"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary" />

            <TextView
                android:id="@+id/text_category"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="دسته‌بندی"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary" />

        </LinearLayout>

        <TextView
            android:id="@+id/text_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="برچسب‌ها"
            android:textSize="12sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/text_created"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="تاریخ ایجاد"
            android:textSize="11sp"
            android:textColor="?android:attr/textColorTertiary"
            android:layout_marginTop="4dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
